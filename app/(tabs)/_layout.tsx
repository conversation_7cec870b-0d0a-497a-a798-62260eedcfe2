import React from 'react';
import {useColorScheme} from '@/hooks/use-color-scheme';
import {Icon, NativeTabs} from "expo-router/unstable-native-tabs";
import {Label} from "@react-navigation/elements";

export default function TabLayout() {
    return (
        <NativeTabs>
            <NativeTabs.Trigger
                name="index"
                options={{
                    title: 'Home',
                }}
            >
                <Icon sf="house.fill"/>
                <Label>Home</Label>
            </NativeTabs.Trigger>
            <NativeTabs.Trigger
                name="explore"
                options={{
                    title: 'Explore',
                }}
            >
                <Icon sf="paperplane.fill"/>
                <Label>Explore</Label>
            </NativeTabs.Trigger>
        </NativeTabs>
    );
}
