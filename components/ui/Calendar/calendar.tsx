import {Calendar, CalendarTheme, toDateId, useCalendar} from "@marceloterreiro/flash-calendar";
import {useCallback, useState} from "react";
import {Button, Text, View} from "react-native";
import {addMonths, startOfMonth, subMonths} from "@marceloterreiro/flash-calendar/src/helpers/dates";
import {ThemedText} from "@/components/themed-text";
import {red} from "react-native-reanimated/lib/typescript/Colors";

const DAY_HEIGHT = 25;
const WEEK_DAYS_HEIGHT = 25;

const calendarTheme: CalendarTheme = {
    itemDay: {
        base: () => ({
            container: {
                backgroundColor: "blue",
                padding: 0,
                borderRadius: 0,
            },
        }),
        today: () => ({
            container: {
                backgroundColor: "red",
            },
        }),
        idle: ({ isDifferentMonth }) => ({
            container: isDifferentMonth
                ? {
                    backgroundColor: "#CCCCCC", // Gray background for different month
                }
                : undefined,
            content: isDifferentMonth
                ? {
                    color: "#666666", // Gray text for different month
                }
                : undefined,
        })
    },
};


export function BasicCalendar() {
    const [currentMonth, setCurrentMonth] = useState(startOfMonth(new Date()));
    const {calendarRowMonth, weekDaysList, weeksList} = useCalendar({
        calendarMaxDateId: "2024-06-31",
        calendarMinDateId: "2024-01-01",
        calendarMonthId: toDateId(currentMonth)
    });

    const onCalendarDayPress = useCallback((dateId: string) => {
        console.log(dateId)
    }, []);

    return (
        <View style={{paddingTop: 20, flex: 1}}>
            <View style={{flexDirection: "row", justifyContent: "space-between", alignItems: "center"}}>
                <ThemedText>{calendarRowMonth}</ThemedText>
                <View style={{flexDirection: "row", alignItems: "center"}}>
                    <Button
                    onPress={() => {
                        const pastMonth = subMonths(currentMonth, 1);
                        setCurrentMonth(pastMonth);
                    }}
                    title="Past"
                />
                    <Button
                        onPress={() => {
                            const nextMonth = addMonths(currentMonth, 1);
                            setCurrentMonth(nextMonth);
                        }}
                        title="Next"
                    />
                </View>

            </View>
            <View style={{flex: 1, width: "100%"}}>
                <Calendar.VStack>
                    {weeksList.map((week, i) => (
                        <Calendar.Row.Week key={i}>
                            {week.map((day) => (
                                <Calendar.Item.Day.Container
                                    dayHeight={DAY_HEIGHT}
                                    daySpacing={4}
                                    isStartOfWeek={day.isStartOfWeek}
                                    key={day.id}
                                >
                                    <Calendar.Item.Day
                                        height={DAY_HEIGHT}
                                        metadata={day}
                                        onPress={onCalendarDayPress}
                                        theme={calendarTheme.itemDay}
                                    >
                                        {day.isToday ? 'T' : day.displayLabel}
                                    </Calendar.Item.Day>
                                </Calendar.Item.Day.Container>
                            ))}
                        </Calendar.Row.Week>
                    ))}
                </Calendar.VStack>
            </View>
        </View>
    );
}